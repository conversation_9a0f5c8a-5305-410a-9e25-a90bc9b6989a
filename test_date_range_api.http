### 测试日期范围课表修复

### 1. 登录获取token
POST http://localhost:8088/timetable/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 创建日期范围课表
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "2025暑假课表",
  "description": "测试日期范围课表",
  "type": "DATE_RANGE",
  "startDate": "2025-07-01",
  "endDate": "2025-07-07"
}

### 3. 批量添加测试数据
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "scheduleDate": "2025-07-01",
    "startTime": "09:00",
    "endTime": "10:00"
  },
  {
    "studentName": "李四",
    "scheduleDate": "2025-07-02", 
    "startTime": "10:00",
    "endTime": "11:00"
  },
  {
    "studentName": "王五",
    "scheduleDate": "2025-07-03",
    "startTime": "11:00", 
    "endTime": "12:00"
  },
  {
    "studentName": "赵六",
    "scheduleDate": "2025-07-04",
    "startTime": "13:00",
    "endTime": "14:00"
  },
  {
    "studentName": "钱七",
    "scheduleDate": "2025-07-05",
    "startTime": "14:00",
    "endTime": "15:00"
  }
]

### 4. 获取第1周的排课数据
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules?week=1
Authorization: Bearer {{token}}

### 5. 获取所有排课数据（不指定周）
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules
Authorization: Bearer {{token}}

### 6. 获取课表详情
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}
Authorization: Bearer {{token}}

### 测试说明
# 1. 2025-07-01是周二，所以第1周应该从2025-06-30(周一)开始
# 2. 第1周包含：2025-06-30 到 2025-07-06
# 3. 我们的测试数据都在第1周内，应该能正确显示
# 4. 前端应该能正确计算周数和显示学生姓名

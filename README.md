# 飓风乒乓培训 - 课程管理系统

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Frontend](https://img.shields.io/badge/frontend-React%2018-61dafb.svg)](timeTable_front/)
[![Backend](https://img.shields.io/badge/backend-Spring%20Boot%202.7-6db33f.svg)](timeTable_back/)
[![Database](https://img.shields.io/badge/database-MySQL%208-4479a1.svg)](timeTable_back/)

## 📖 项目简介

飓风乒乓培训课程管理系统是一款专为乒乓球培训机构设计的智能排课管理应用。系统支持语音录入、可视化课表管理、多用户权限控制等功能，旨在提高培训机构的课程管理效率。

**飓风乒乓培训** - 专业保障快乐提高 🏓

## ✨ 核心功能

### 🎯 前端功能
- 🔐 用户认证系统（登录/注册）
- 📅 可视化课表管理
- 🎤 语音录入课程信息
- 📱 移动端H5完美适配
- 👨‍💼 管理员权限管理
- 🌙 现代化UI界面

### ⚡ 后端功能
- 🔒 JWT安全认证
- 📊 RESTful API设计
- 🗄️ 数据库版本控制
- 🤖 AI语音处理集成
- 📈 课表数据分析
- 🔧 系统监控和日志

## 🛠️ 技术架构

### 前端技术栈
- **框架**: React 18.2.0
- **构建工具**: Vite 4.4.5
- **UI库**: Ant Design 5.10.0
- **路由**: React Router DOM 6.15.0
- **HTTP客户端**: Axios 1.5.0

### 后端技术栈
- **框架**: Spring Boot 2.7.18
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0 + JPA
- **版本控制**: Flyway 7.14.0
- **构建工具**: Maven 3.6+

## 🏗️ 项目结构

```
timetable/
├── timeTable_front/           # 前端项目 (React + Vite)
│   ├── src/
│   │   ├── components/       # 通用组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务层
│   │   └── ...
│   ├── public/              # 静态资源
│   ├── package.json         # 前端依赖配置
│   └── README.md           # 前端文档
├── timeTable_back/           # 后端项目 (Spring Boot)
│   ├── src/main/java/       # Java源码
│   ├── src/main/resources/  # 配置文件
│   ├── pom.xml             # Maven配置
│   └── README.md           # 后端文档
├── docs/                    # 项目文档
└── README.md               # 项目总览 (本文件)
```

## 🚀 快速开始

### 📋 环境要求

- **前端**: Node.js >= 16.0, npm >= 7.0
- **后端**: Java 8+, Maven 3.6+, MySQL 8.0+

### 1️⃣ 克隆项目

```bash
git clone https://github.com/your-username/timetable.git
cd timetable
```

### 2️⃣ 后端启动

```bash
cd timeTable_back

# 配置数据库
mysql -u root -p
CREATE DATABASE timetable_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 修改 application.yml 中的数据库配置
# 安装依赖并启动
mvn clean install
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

### 3️⃣ 前端启动

```bash
cd timeTable_front

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 `http://localhost:3000` 启动

### 4️⃣ 访问应用

打开浏览器访问 `http://localhost:3000`，使用测试账号登录：

- **普通用户**: `testuser` / `password123`
- **管理员**: `admin` / `admin123`

## 📱 功能展示

### 登录界面
- 品牌Logo展示
- 用户认证
- 测试账号快速登录

### 课表管理
- 创建新课表
- 可视化表格显示
- 支持周分页查看

### 语音录入
- 语音识别技术
- 智能课程安排
- 实时反馈

### 管理面板
- 用户管理
- 课表合并
- 数据统计

## 🧪 测试和开发

### 前端开发

```bash
cd timeTable_front

# 开发模式
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview
```

### 后端开发

```bash
cd timeTable_back

# 运行测试
mvn test

# 数据库迁移
mvn flyway:migrate

# 打包
mvn clean package
```

## 📦 部署指南

### 前端部署

```bash
# 构建生产版本
npm run build

# 部署到静态服务器
# dist/ 目录包含所有静态文件
```

支持部署到：
- Nginx
- Apache
- CDN服务
- 静态托管服务

### 后端部署

```bash
# 打包应用
mvn clean package

# 运行JAR包
java -jar target/timetable-backend-1.0.0.jar

# 或使用Docker
docker build -t timetable-backend .
docker run -p 8080:8080 timetable-backend
```

## 📊 API文档

后端API文档可通过以下方式访问：

- **Swagger UI**: `http://localhost:8080/swagger-ui.html` (开发中)
- **API文档**: 详见 [timeTable_back/README.md](timeTable_back/README.md)

主要接口包括：
- 用户认证 (`/auth/*`)
- 课表管理 (`/timetables/*`)
- 管理功能 (`/admin/*`)

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 提交规范

```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建或辅助工具变动
```

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 文档链接

- 📚 [前端文档](timeTable_front/README.md)
- 📚 [后端文档](timeTable_back/README.md)
- 🔧 [部署指南](docs/deployment.md) (待完善)
- 🔌 [API参考](docs/api.md) (待完善)

## 📝 更新日志

### v1.0.0 (2024-12-30)

#### ✨ 新功能
- 🎉 项目初始化
- 🔐 完整的用户认证系统
- 📅 课表创建和管理功能
- 🎤 语音录入课程信息
- 📱 移动端H5适配
- 👨‍💼 管理员权限控制

#### 🛠️ 技术实现
- ⚡ Vite构建工具，超快的开发体验
- 🎨 Ant Design现代化UI组件
- 🔒 Spring Security + JWT安全认证
- 🗄️ Flyway数据库版本控制
- 📊 RESTful API设计

## 📞 联系方式

- **项目地址**: [GitHub Repository](https://github.com/your-username/timetable)
- **问题反馈**: [GitHub Issues](https://github.com/your-username/timetable/issues)
- **邮箱**: <EMAIL>

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

---

<div align="center">

**飓风乒乓培训** - 专业保障快乐提高 🏓

Made with ❤️ by [Your Team Name]

</div> 
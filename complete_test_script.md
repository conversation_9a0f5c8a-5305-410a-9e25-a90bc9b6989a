# 完整功能测试脚本

## 测试目标
1. 验证日期范围课表显示修复
2. 验证时间平铺功能

## 测试步骤

### 1. 启动服务
```bash
# 启动后端
cd timeTable_back
./mvnw spring-boot:run

# 启动前端
cd timeTable_front  
npm run dev
```

### 2. 创建测试课表
1. 登录系统 (admin/admin123)
2. 创建日期范围课表
   - 名称：2025年7月测试课表
   - 类型：日期范围课表
   - 开始日期：2025-07-01
   - 结束日期：2025-07-31

### 3. 测试时间平铺功能
在录入页面使用格式解析模式，输入：
```
7.11-7.20，5-7，张小啼
```

**预期结果：**
- 解析出20个课程安排（10天 × 2小时）
- 每天有两个时间段：17:00-18:00, 18:00-19:00
- 学生姓名：张小啼

### 4. 测试多学生输入
```
7.11-7.15，9-11，李小明
7.16-7.20，14-16，王小红
```

**预期结果：**
- 李小明：5天 × 2小时 = 10个课程安排
- 王小红：5天 × 2小时 = 10个课程安排

### 5. 验证课表显示
1. 确认课程安排后，查看课表
2. 切换到第2周（7.7-7.13），应该显示7.11-7.13的课程
3. 切换到第3周（7.14-7.20），应该显示7.14-7.20的课程

## 测试检查点

### 时间平铺功能
- [ ] 输入"5-7"能正确解析为"17:00-19:00"
- [ ] 时间范围能正确拆分为1小时单元
- [ ] 日期范围能正确展开为每一天
- [ ] 学生姓名正确识别

### 日期范围课表显示
- [ ] 周数计算正确（周一为每周第一天）
- [ ] 学生姓名正确显示在对应时间格子中
- [ ] 日期显示正确（如07-11显示在周五）
- [ ] 分页功能正常工作

### 边界情况测试
- [ ] 跨月日期范围（如7.25-8.5）
- [ ] 单天课程（如7.15，9-10，张三）
- [ ] 非整点时间（如15:30-16:30）

## 预期问题和解决方案

### 问题1：时间解析错误
如果"5-7"被解析为"05:00-07:00"而不是"17:00-19:00"，需要检查parseTime方法的时间推断逻辑。

### 问题2：日期格式不识别
如果"7.11-7.20"不能被识别，检查isDayPart方法的正则表达式。

### 问题3：学生姓名位置错误
如果学生姓名不能正确识别，检查parseSingleLine方法的解析顺序逻辑。

## 成功标准
1. 输入"7.11-7.20，5-7，张小啼"能生成20个正确的课程安排
2. 课表能正确显示所有学生姓名
3. 周数和日期计算准确
4. 用户界面友好，操作流畅

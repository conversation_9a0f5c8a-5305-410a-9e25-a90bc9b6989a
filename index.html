<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能排课助手 - 页面原型预览 v12 (Complete)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #e9ecf1;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 40px;
            padding: 40px;
        }

        .prototype-title {
            width: 100%;
            text-align: center;
            font-size: 2em;
            font-weight: 500;
            color: #333;
            margin-bottom: 20px;
        }

        /* --- Phone Mockup --- */
        .phone-mockup {
            width: 375px;
            height: 812px; /* iPhone X/11/12 screen ratio */
            border: 16px solid #111;
            border-radius: 40px;
            background-color: #fff;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .phone-mockup::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 210px;
            height: 30px;
            background-color: #111;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
            z-index: 20;
        }

        /* --- Page Styles --- */
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5f7fa;
            --font-color: #333;
            --light-font-color: #777;
            --border-color: #e0e0e0;
            --white-color: #ffffff;
        }

        .page-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            width: 100%;
            background-color: var(--white-color);
        }
        
        .header {
            width: 100%;
            padding: 15px 20px;
            padding-top: 45px; /* Space for the notch */
            background-color: var(--primary-color);
            color: var(--white-color);
            text-align: center;
            font-size: 1.1em;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .header-title {
            flex-grow: 1;
        }

        .content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .button {
            display: block;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background-color: var(--primary-color);
            color: var(--white-color);
            font-size: 1em;
            font-weight: 500;
            text-align: center;
            cursor: default;
            width: 100%;
            text-decoration: none;
        }

        .button-secondary {
            background-color: var(--white-color);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--light-font-color);
            font-size: 0.9em;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 1em;
        }
        
        .link {
            color: var(--primary-color);
            cursor: default;
            text-align: center;
            display: block;
            margin-top: 15px;
        }

        .app-title {
            text-align: center;
            font-size: 2.2em;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 40px;
        }

        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            font-size: 0.8em;
        }
        .schedule-table th, .schedule-table td {
            border: 1px solid var(--border-color);
            padding: 4px 2px;
            height: 40px;
        }
        .schedule-table thead th {
            font-weight: 500;
            font-size:0.9em;
            padding: 8px 2px;
        }
        .schedule-table tbody th {
            font-weight: 400;
            font-size:0.8em;
            color: var(--light-font-color);
        }

        .schedule-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .schedule-nav-btn {
            background: none;
            border: none;
            font-size: 1.5em;
            color: var(--primary-color);
        }
        .schedule-nav-date {
            font-size: 1.1em;
            font-weight: 500;
        }

        .top-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            gap: 10px;
        }
        .top-actions .button {
            width: auto;
            flex-grow: 1;
            padding: 8px 10px;
            font-size: 0.9em;
        }

        .header-icon {
            font-size: 1.2em;
            color: white;
            text-decoration: none;
            position: absolute;
            top: 50%;
            padding-top: 15px;
            transform: translateY(-50%);
        }
        .header .back-arrow { left: 20px; font-size: 1.5em; }
        .header .add-btn { right: 20px; font-size: 1.8em; font-weight: 300; }
        .header .user-icon { left: 20px; }
        
        .schedule-list-item {
            background-color: var(--secondary-color);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .schedule-list-item h3 {
            font-size: 1.1em;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .schedule-list-item p {
            font-size: 0.9em;
            color: var(--light-font-color);
        }
        .schedule-list-item .actions a {
            margin-left: 10px;
            color: var(--primary-color);
            text-decoration: none;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .tab-nav {
            display: flex;
            background-color: var(--secondary-color);
            border-radius: 8px;
            padding: 5px;
            margin-bottom: 20px;
        }
        .tab-nav .tab {
            flex: 1;
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            cursor: default;
            font-weight: 500;
        }
        .tab-nav .tab.active {
            background-color: var(--primary-color);
            color: var(--white-color);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .shared-icon, .shared-by-me-icon {
            font-size: 0.7em;
            padding: 2px 5px;
            border-radius: 10px;
            color: white;
        }
        .shared-icon {
            background-color: #27ae60; /* green */
        }
        .shared-by-me-icon {
             background-color: #e67e22; /* orange */
        }


    </style>
</head>
<body>
    <h1 class="prototype-title">智能排课助手 - H5页面原型 v12 (Complete)</h1>

    <!-- Prototype 1: Login Page -->
    <div class="phone-mockup">
        <div class="page-content" style="background-color: var(--secondary-color);">
            <div class="content" style="display: flex; flex-direction: column; justify-content: center;">
                <h1 class="app-title">智能排课助手</h1>
                <div class="input-group">
                    <label for="username">用户名</label>
                    <input type="text" class="input-field" placeholder="请输入用户名">
                </div>
                <div class="input-group">
                    <label for="password">密码</label>
                    <input type="password" class="input-field" placeholder="请输入密码">
                </div>
                <a href="#" class="button">登录</a>
                <p class="link">没有账号？立即注册</p>
            </div>
        </div>
    </div>
    
    <!-- Prototype 2: Register Page -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                <a href="#" class="header-icon back-arrow"><</a>
                <span class="header-title">创建新账号</span>
            </div>
            <div class="content">
                <div class="input-group">
                    <label for="reg-username">用户名</label>
                    <input type="text" class="input-field" placeholder="设置您的用户名">
                </div>
                <div class="input-group">
                    <label for="reg-password">密码</label>
                    <input type="password" class="input-field" placeholder="设置您的密码 (至少6位)">
                </div>
                <div class="input-group">
                    <label for="reg-confirm-password">确认密码</label>
                    <input type="password" class="input-field" placeholder="请再次输入密码">
                </div>
                <a href="#" class="button" style="margin-top: 10px;">注册</a>
            </div>
        </div>
    </div>
    
    <!-- Prototype 3: Schedule List Page -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                <a href="#" class="header-icon user-icon">👤</a>
                <span class="header-title">我的课表</span>
                <a href="#" class="header-icon add-btn">+</a>
            </div>
            <div class="content">
                <div class="tab-nav">
                    <div class="tab active">我的课表</div>
                    <div class="tab">收到的分享</div>
                </div>
                <div class="my-schedules-content">
                    <div class="schedule-list-item">
                        <div>
                            <h3>暑假课表 <span style="font-size:0.8em; color: var(--light-font-color); font-weight: 400;">(A101)</span></h3>
                            <p>2025-07-15 ~ 2025-08-31</p>
                        </div>
                        <div class="actions"> <a href="#">进入</a> </div>
                    </div>
                    <div class="schedule-list-item">
                        <div>
                            <h3>开学课表 <span class="shared-by-me-icon">已分享</span></h3>
                            <p>2025-09-01 ~ 2026-01-15</p>
                        </div>
                        <div class="actions"> <a href="#">进入</a> </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Prototype 4: Add by Share Code Page -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                <a href="#" class="header-icon back-arrow"><</a>
                <span class="header-title">查看分享</span>
            </div>
            <div class="content">
                <div class="input-group">
                    <label for="share-code">分享码</label>
                    <input type="text" id="share-code" class="input-field" placeholder="粘贴或输入分享码">
                </div>
                <a href="#" class="button">查看课表</a>
                 <div class="shared-content" style="margin-top: 40px; text-align: center; color: var(--light-font-color);">
                    <p>-- 查找到的课表将在此显示 --</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Prototype 5: Date-based Schedule Detail -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                <a href="#" class="header-icon back-arrow"><</a>
                <span class="header-title">开学课表</span>
            </div>
            <div class="content">
                <div class="top-actions">
                    <a href="#" class="button">添加课程</a>
                    <a href="#" class="button button-secondary">分享</a>
                </div>
                <div class="schedule-nav">
                    <button class="schedule-nav-btn"><</button>
                    <span class="schedule-nav-date">9月1日 - 9月7日</span>
                    <button class="schedule-nav-btn">></button>
                </div>
                <table class="schedule-table">
                     <thead>
                        <tr><th></th><th>一<br><small>1</small></th><th>二<br><small>2</small></th><th>三<br><small>3</small></th><th>四<br><small>4</small></th><th>五<br><small>5</small></th><th>六<br><small>6</small></th><th>日<br><small>7</small></th></tr>
                    </thead>
                    <tbody>
                        <tr><th>10:00</th><td></td><td style="background:#eaf4ff;">高数</td><td></td><td style="background:#eaf4ff;">高数</td><td></td><td></td><td></td></tr>
                        <tr><th>14:00</th><td></td><td></td><td style="background:#eaf4ff;">C语言</td><td></td><td style="background:#eaf4ff;">C语言</td><td></td><td></td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Prototype 6: Week-based Schedule Detail -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                 <a href="#" class="header-icon back-arrow"><</a>
                <span class="header-title">每周固定日程 (W007)</span>
            </div>
            <div class="content">
                <div class="top-actions">
                    <a href="#" class="button">添加课程</a>
                    <a href="#" class="button button-secondary">分享</a>
                </div>
                <table class="schedule-table" style="margin-top: 15px;">
                    <thead>
                        <tr><th></th><th>周一</th><th>周二</th><th>周三</th><th>周四</th><th>周五</th><th>周六</th><th>周日</th></tr>
                    </thead>
                    <tbody>
                        <tr><th>09:00</th><td></td><td></td><td></td><td></td><td style="background:#eaf4ff;">健身</td><td></td><td></td></tr>
                        <tr><th>19:00</th><td style="background:#eaf4ff;">英语课</td><td></td><td style="background:#eaf4ff;">英语课</td><td></td><td></td><td></td><td></td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Prototype 7: Create New Schedule Page -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                 <a href="#" class="header-icon back-arrow"><</a>
                <span class="header-title">创建新课表</span>
            </div>
            <div class="content">
                <div class="input-group">
                    <label for="schedule-title">课表标题</label>
                    <input type="text" class="input-field" placeholder="例如：2025年暑期班">
                </div>
                <div class="input-group">
                    <label for="start-date">开始日期</label>
                    <input type="date" class="input-field">
                </div>
                <div class="input-group">
                    <label for="end-date">结束日期</label>
                    <input type="date" class="input-field">
                </div>
                <div class="input-group checkbox-group">
                    <input type="checkbox" id="is-weekly-fixed" style="width: 20px; height: 20px;">
                    <label for="is-weekly-fixed" style="margin-bottom: 0;">是否为周固定课表</label>
                </div>
                 <div class="actions" style="margin-top: 20px;">
                    <a href="#" class="button">创建并进入</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Prototype 8: Add Class Entry Page -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                 <a href="#" class="header-icon back-arrow"><</a>
                <span class="header-title">添加课程到“开学课表”</span>
            </div>
            <div class="content">
                <div class="input-group">
                    <label for="class-info">课程信息 (文字或语音输入)</label>
                    <textarea class="input-field" rows="4" placeholder="例如：周二上午10点 高数"></textarea>
                </div>
                <button class="button button-secondary">🎤 按住说话</button>
                 <div class="actions" style="margin-top: 20px; display: flex; gap: 15px;">
                    <a href="#" class="button">保存</a>
                    <a href="#" class="button button-secondary">取消</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Prototype 9: Share Options Page -->
    <div class="phone-mockup">
        <div class="page-content">
            <div class="header">
                 <a href="#" class="header-icon back-arrow"><</a>
                <span class="header-title">分享课表</span>
            </div>
            <div class="content">
                <div class="input-group">
                    <label>选择要分享的课表</label>
                    <select class="input-field">
                        <option>开学课表 (B234)</option>
                        <option>暑假课表 (A101)</option>
                        <option>每周固定日程 (W007)</option>
                    </select>
                </div>
                 <div class="input-group">
                    <label>分享码 (24小时有效)</label>
                    <input type="text" class="input-field" value="B234-XYZ-789" readonly style="text-align:center; font-weight: 500; letter-spacing: 2px;">
                </div>
                <a href="#" class="button">复制分享码</a>
            </div>
        </div>
    </div>

</body>
</html>
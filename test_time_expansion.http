### 测试时间平铺功能

### 1. 登录获取token
POST http://localhost:8088/timetable/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 创建日期范围课表
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "2025年7月课表",
  "description": "测试时间平铺功能",
  "type": "DATE_RANGE",
  "startDate": "2025-07-01",
  "endDate": "2025-07-31"
}

### 3. 测试格式化文本解析 - 时间平铺
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/format
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "text": "7.11-7.20，5-7，张小啼",
  "type": "DATE_RANGE"
}

### 4. 测试多行输入
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/format
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "text": "7.11-7.15，9-11，李小明\n7.16-7.20，14-16，王小红",
  "type": "DATE_RANGE"
}

### 5. 测试AI文本解析 - 时间平铺
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/text
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "text": "张小啼从7月11日到7月20日，每天下午5点到7点上课",
  "type": "DATE_RANGE"
}

### 6. 获取解析结果并批量创建
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张小啼",
    "scheduleDate": "2025-07-11",
    "startTime": "17:00",
    "endTime": "18:00"
  },
  {
    "studentName": "张小啼",
    "scheduleDate": "2025-07-11", 
    "startTime": "18:00",
    "endTime": "19:00"
  }
]

### 7. 查看课表数据
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules
Authorization: Bearer {{token}}

### 测试预期结果
# 输入："7.11-7.20，5-7，张小啼"
# 应该解析为：
# - 日期范围：2025-07-11 到 2025-07-20 (10天)
# - 时间范围：17:00-19:00 (下午5-7点)
# - 学生：张小啼
# - 平铺后：每天2个时间段 × 10天 = 20个课程安排
#   - 17:00-18:00
#   - 18:00-19:00

@token = eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzZ3pzIiwiZXhwIjoxODM3OTU3MzIwLCJpYXQiOjE3NTE1NTczMjB9.pQCo8mQNjuCcE6zPlSklO7nD0EC978Gm4UwbJImJCR6uuvXUtq-pg_s7pRavxQdhb9Dp8ytrOVHXCTf8oK9eMw

### 用户登录
POST http://localhost:8088/timetable/api/auth/login
Content-Type: application/json

{
  "username": "sgzs",
  "password": "1234567"
}

### 用户注册
POST http://localhost:8088/timetable/api/auth/register
Content-Type: application/json

{
  "username": "new2user12",
  "password": "newpass"
}

### 验证Token
GET http://localhost:8088/timetable/api/auth/validate
Authorization: Bearer {{token}}

### 获取当前用户信息
GET http://localhost:8088/timetable/api/auth/me
Authorization: Bearer {{token}}

### 用户登出
POST http://localhost:8088/timetable/api/auth/logout
Authorization: Bearer {{token}}

### 新建周课表
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
  "name": "测试周课表",
  "description": "自动化测试周课表",
  "type": "WEEKLY"
}

### 新建日期课表
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "测试日期课表",
  "description": "自动化测试日期课表",
  "type": "DATE_RANGE",
  "startDate": "2024-07-01",
  "endDate": "2024-07-31"
}

### 获取课表列表
GET http://localhost:8088/timetable/api/timetables
Authorization: Bearer {{token}}

### 批量创建排课
POST http://localhost:8088/timetable/api/timetables/1/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "于乐之",
    "dayOfWeek": "THURSDAY",
    "startTime": "13:00",
    "endTime": "14:00",
    "note": "通过文本识别创建"
  },
  {
    "studentName": "于乐之",
    "dayOfWeek": "THURSDAY",
    "startTime": "14:00",
    "endTime": "15:00",
    "note": "通过文本识别创建"
  },
  {
    "studentName": "张早早",
    "dayOfWeek": "FRIDAY",
    "startTime": "13:00",
    "endTime": "14:00",
    "note": "通过文本识别创建"
  }
]

### 获取排课列表
GET http://localhost:8088/timetable/api/timetables/4/schedules
Authorization: Bearer {{token}}

### 按条件批量删除排课
DELETE http://localhost:8088/timetable/api/timetables/1/schedules
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "studentName": "于乐之",
  "dayOfWeek": "THURSDAY",
  "startTime": "13:00",
  "endTime": "14:00"
}

### 批量按条件删除排课
DELETE http://localhost:8088/timetable/api/timetables/1/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "于乐之",
    "dayOfWeek": "THURSDAY",
    "startTime": "13:00",
    "endTime": "14:00"
  },
  {
    "studentName": "张早早",
    "dayOfWeek": "FRIDAY",
    "startTime": "13:00",
    "endTime": "14:00"
  }
]

### 获取第1周的排课（本地测试）
GET http://localhost:8088/timetable/api/timetables/6/schedules?week=1
Authorization: Bearer {{token}} 


### 获取第1周的排课（本地测试）

POST http://localhost:8088/timetable/api/timetables/4/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "scheduleDate": "2024-07-01",
    "startTime": "13:00",
    "endTime": "14:00"
  },
  {
    "studentName": "李四",
    "scheduleDate": "2024-07-02",
    "startTime": "15:00",
    "endTime": "16:00"
  }
]
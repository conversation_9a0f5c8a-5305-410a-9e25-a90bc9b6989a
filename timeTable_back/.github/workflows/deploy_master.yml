name: deploy_master_back
on:
  push:
    branches:
      - master

jobs:
  master-timeTable-back:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: JDK 8
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Flyway Clean
        if: ${{ vars.FLYWAY_CLEAN == '1' }}
        run: mvn flyway:clean -P test
      - name: Build application lesson_web_back
        run: mvn flyway:repair -P test && mvn flyway:migrate -P test && mvn jooq-codegen:generate -P test && mvn clean compile -P test && mvn install -P test -Dmaven.test.skip=true
      - name: Scp jar To HW Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HW_TEST_SERVER }}
          username: ${{ secrets.HW_SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          source: ${{ github.workspace }}/target/timetable-backend-1.0.0.jar
          target: /root/lesson
          strip_components: 6
      - name: Run Java Service Of timetable-back on HW Server
        uses: fifsky/ssh-action@master
        with:
          command: supervisorctl status timetable-back | awk '{print $2}' | while read line; do if [ $line == 'RUNNING' ]; then supervisorctl restart timetable-back;else supervisorctl start timetable-back; fi done
          host: ${{ secrets.HW_TEST_SERVER }}
          user: ${{ secrets.HW_SERVER_USERNAME }}
          pass: ${{ secrets.HW_SERVER_PWD }}
          args: "-tt"

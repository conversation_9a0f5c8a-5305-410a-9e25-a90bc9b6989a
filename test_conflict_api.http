### 测试冲突检测API

### 1. 登录获取token
POST http://localhost:8088/timetable/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 获取课表列表
GET http://localhost:8088/timetable/api/timetables
Authorization: Bearer {{token}}

### 3. 测试冲突检测API - 简单测试
POST http://localhost:8088/timetable/api/timetables/1/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "测试学生",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "测试冲突检测"
  }
]

### 4. 测试批量创建API（对比）
POST http://localhost:8088/timetable/api/timetables/1/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "测试学生2",
    "dayOfWeek": "TUESDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "测试正常创建"
  }
]

### 5. 测试强制创建API
POST http://localhost:8088/timetable/api/timetables/1/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "测试学生3",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "测试强制创建"
  }
]

### 选择性冲突覆盖功能API测试

### 1. 登录获取token
POST http://localhost:8088/timetable/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 创建测试课表
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "选择性覆盖测试课表",
  "description": "测试选择性冲突覆盖功能",
  "type": "WEEKLY"
}

### 3. 创建基础排课数据（用于产生冲突）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "基础数据1"
  },
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:00",
    "endTime": "15:00",
    "note": "基础数据2"
  },
  {
    "studentName": "赵六",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "16:00",
    "endTime": "17:00",
    "note": "基础数据3"
  }
]

### 4. 测试多冲突场景（部分创建 + 多个冲突）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "王五",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "与张三冲突"
  },
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:30",
    "endTime": "15:30",
    "note": "李四自己冲突"
  },
  {
    "studentName": "孙七",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "16:00",
    "endTime": "17:00",
    "note": "与赵六冲突"
  },
  {
    "studentName": "陈八",
    "dayOfWeek": "THURSDAY",
    "startTime": "18:00",
    "endTime": "19:00",
    "note": "无冲突"
  }
]

### 预期响应：
# {
#   "success": true,
#   "message": "部分创建成功，发现冲突",
#   "data": {
#     "hasConflicts": true,
#     "createdSchedules": [
#       {
#         "studentName": "陈八",
#         "dayOfWeek": "THURSDAY",
#         "startTime": "18:00",
#         "endTime": "19:00"
#       }
#     ],
#     "conflicts": [
#       {
#         "conflictType": "TIME_SLOT_CONFLICT",
#         "conflictDescription": "时间段 周一 09:00-10:00 已被学生 张三 占用，新学生 王五 产生冲突",
#         "newScheduleRequest": { "studentName": "王五", ... }
#       },
#       {
#         "conflictType": "STUDENT_TIME_CONFLICT", 
#         "conflictDescription": "学生 李四 在 周二 14:30-15:30 已有课程安排",
#         "newScheduleRequest": { "studentName": "李四", ... }
#       },
#       {
#         "conflictType": "TIME_SLOT_CONFLICT",
#         "conflictDescription": "时间段 周三 16:00-17:00 已被学生 赵六 占用，新学生 孙七 产生冲突", 
#         "newScheduleRequest": { "studentName": "孙七", ... }
#       }
#     ]
#   }
# }

### 5. 模拟选择性覆盖 - 只覆盖第1和第3个冲突（王五和孙七）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "王五",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "通过文本识别创建（智能覆盖）"
  },
  {
    "studentName": "孙七",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "16:00",
    "endTime": "17:00",
    "note": "通过文本识别创建（智能覆盖）"
  }
]

### 6. 验证最终结果
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules
Authorization: Bearer {{token}}

### 预期最终结果：
# 1. 李四，周二 14:00-15:00 (原有，未被覆盖)
# 2. 陈八，周四 18:00-19:00 (无冲突，已创建)
# 3. 王五，周一 09:00-10:00 (覆盖了张三)
# 4. 孙七，周三 16:00-17:00 (覆盖了赵六)
# 注意：李四的冲突未被处理，所以没有李四的14:30-15:30排课

### 7. 测试全选覆盖场景
# 重新创建基础数据
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "全选覆盖测试课表",
  "description": "测试全选覆盖功能",
  "type": "WEEKLY"
}

### 8. 为新课表创建基础数据
POST http://localhost:8088/timetable/api/timetables/{{newTimetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "基础数据"
  },
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY", 
    "startTime": "14:00",
    "endTime": "15:00",
    "note": "基础数据"
  }
]

### 9. 测试全选覆盖
POST http://localhost:8088/timetable/api/timetables/{{newTimetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "王五",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00", 
    "endTime": "10:00",
    "note": "冲突1"
  },
  {
    "studentName": "赵六",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:00",
    "endTime": "15:00", 
    "note": "冲突2"
  }
]

### 10. 全选强制覆盖
POST http://localhost:8088/timetable/api/timetables/{{newTimetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "王五",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "通过文本识别创建（智能覆盖）"
  },
  {
    "studentName": "赵六", 
    "dayOfWeek": "TUESDAY",
    "startTime": "14:00",
    "endTime": "15:00",
    "note": "通过文本识别创建（智能覆盖）"
  }
]

### 11. 验证全选覆盖结果
GET http://localhost:8088/timetable/api/timetables/{{newTimetableId}}/schedules
Authorization: Bearer {{token}}

### 预期结果：
# 1. 王五，周一 09:00-10:00 (覆盖了张三)
# 2. 赵六，周二 14:00-15:00 (覆盖了李四)

### 12. 测试空选择场景（前端应该阻止，但后端也要处理）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[]

### 预期响应：
# {
#   "success": true,
#   "message": "强制创建排课成功",
#   "data": []
# }

### 测试总结
# 1. 部分冲突 + 选择性覆盖：✓ 只处理选中的冲突
# 2. 全选覆盖：✓ 处理所有冲突
# 3. 空选择：✓ 不处理任何冲突
# 4. 结果验证：✓ 数据库状态符合预期

### 前端测试要点
# 1. 复选框状态管理正确
# 2. 全选/半选/取消全选功能正常
# 3. 按钮状态和文案动态更新
# 4. 只发送选中冲突的请求
# 5. 结果提示信息准确

# 智能覆盖功能测试

## 功能说明

修改了强制覆盖的逻辑，实现智能覆盖：

1. **不同学员的时间冲突**：删除原有排课，添加新排课（真正的覆盖）
2. **同一学员的重复排课**：保留原有排课，添加新排课（允许重复）

## 测试场景

### 场景1：不同学员时间冲突（应该删除原有）

**前置条件**：
- 已有排课：张三，周一 9:00-10:00

**测试输入**：
```
李四，周一，9:00-10:00
```

**预期结果**：
1. 检测到冲突：时间段冲突
2. 强制覆盖后：
   - 删除张三的原有排课
   - 添加李四的新排课
   - 最终只有李四 9:00-10:00

### 场景2：同一学员重复时间（应该保留原有）

**前置条件**：
- 已有排课：张三，周一 9:00-10:00

**测试输入**：
```
张三，周一，9:30-10:30
```

**预期结果**：
1. 检测到冲突：学生时间冲突
2. 强制覆盖后：
   - 保留张三的原有排课 9:00-10:00
   - 添加张三的新排课 9:30-10:30
   - 最终张三有两个重叠的时间段

### 场景3：混合冲突情况

**前置条件**：
- 已有排课：张三，周一 9:00-10:00
- 已有排课：李四，周二 14:00-15:00

**测试输入**：
```
王五，周一，9:00-10:00
张三，周一，9:30-10:30
赵六，周二，14:00-15:00
```

**预期结果**：
1. 检测到多个冲突
2. 强制覆盖后：
   - 删除张三的周一 9:00-10:00（被王五覆盖）
   - 保留并添加张三的周一 9:30-10:30（同学员重复）
   - 删除李四的周二 14:00-15:00（被赵六覆盖）
   - 最终结果：
     - 王五，周一 9:00-10:00
     - 张三，周一 9:30-10:30
     - 赵六，周二 14:00-15:00

## API测试

### 1. 创建基础数据
```http
POST /api/timetables/1/schedules/batch
[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00"
  },
  {
    "studentName": "李四", 
    "dayOfWeek": "TUESDAY",
    "startTime": "14:00",
    "endTime": "15:00"
  }
]
```

### 2. 测试智能覆盖
```http
POST /api/timetables/1/schedules/batch/force
[
  {
    "studentName": "王五",
    "dayOfWeek": "MONDAY", 
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "测试不同学员覆盖"
  },
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:30", 
    "endTime": "10:30",
    "note": "测试同学员重复"
  }
]
```

### 3. 验证结果
```http
GET /api/timetables/1/schedules
```

**预期响应**：应该看到王五覆盖了张三的原有时间，但张三有新的重复时间段。

## 前端界面变化

### 冲突提示说明
```
选择操作
您可以选择取消本次操作，或者强制覆盖。强制覆盖规则：
- 如果是不同学员的时间冲突，将删除原有课程并添加新课程
- 如果是同一学员的重复时间，将保留原有课程并添加新课程
```

### 按钮文案
- 保持"强制覆盖"不变
- 创建备注改为"通过文本识别创建（智能覆盖）"

## 技术实现

### 后端新增方法
- `ScheduleService.createSchedulesWithOverride()` - 智能覆盖逻辑
- 根据学员姓名判断是否删除原有排课

### 日志记录
删除冲突排课时会记录详细日志：
```
删除冲突排课: 学生=张三, 时间=09:00-10:00, ID=123
```

## 验证清单
- [ ] 不同学员冲突时删除原有排课
- [ ] 同一学员重复时保留原有排课  
- [ ] 混合场景处理正确
- [ ] 日志记录完整
- [ ] 前端提示信息准确
- [ ] API响应正常

{"name": "timetable-backend", "version": "1.0.0", "description": "语音排课系统后端API", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build": "echo 'Build completed'", "test": "echo 'Tests not implemented yet'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "sequelize": "^6.32.1", "sqlite3": "^5.1.6", "mysql2": "^3.6.0", "express-validator": "^7.0.1", "express-rate-limit": "^6.10.0", "axios": "^1.5.0", "openai": "^4.12.4", "form-data": "^4.0.0", "node-cron": "^3.0.2", "dayjs": "^1.11.9", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["nodejs", "express", "api", "timetable", "voice", "ai", "scheduling"], "author": "System", "license": "MIT"}
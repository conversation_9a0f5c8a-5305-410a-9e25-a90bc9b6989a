#!/bin/bash

BASE_URL="http://localhost:8088/timetable/api"

echo "=== 课表系统API测试 ==="

# 1. 注册用户
echo "1. 注册用户..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456",
    "email": "<EMAIL>"
  }')
echo "注册响应: $REGISTER_RESPONSE"

# 提取token
TOKEN=$(echo $REGISTER_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "获取到的Token: $TOKEN"

if [ -z "$TOKEN" ]; then
  echo "注册失败，退出测试"
  exit 1
fi

echo ""

# 2. 获取用户信息
echo "2. 获取用户信息..."
curl -s -X GET "$BASE_URL/auth/me" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""

# 3. 创建课表
echo "3. 创建课表..."
TIMETABLE_RESPONSE=$(curl -s -X POST "$BASE_URL/timetables" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "测试课表",
    "description": "这是一个测试课表",
    "isWeekly": true
  }')
echo "创建课表响应: $TIMETABLE_RESPONSE"

# 提取课表ID
TIMETABLE_ID=$(echo $TIMETABLE_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "获取到的课表ID: $TIMETABLE_ID"

echo ""

# 4. 获取课表列表
echo "4. 获取课表列表..."
curl -s -X GET "$BASE_URL/timetables" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""

# 5. 获取课表详情
echo "5. 获取课表详情..."
curl -s -X GET "$BASE_URL/timetables/$TIMETABLE_ID" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""

# 6. 创建排课
echo "6. 创建排课..."
SCHEDULE_RESPONSE=$(curl -s -X POST "$BASE_URL/timetables/$TIMETABLE_ID/schedules" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "studentName": "张三",
    "subject": "数学",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00:00",
    "endTime": "10:00:00",
    "note": "测试课程"
  }')
echo "创建排课响应: $SCHEDULE_RESPONSE"

# 提取排课ID
SCHEDULE_ID=$(echo $SCHEDULE_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "获取到的排课ID: $SCHEDULE_ID"

echo ""

# 7. 获取课表的排课列表
echo "7. 获取课表的排课列表..."
curl -s -X GET "$BASE_URL/timetables/$TIMETABLE_ID/schedules" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""

# 8. 通过文本输入创建排课
echo "8. 通过文本输入创建排课..."
curl -s -X POST "$BASE_URL/timetables/$TIMETABLE_ID/schedules/text" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "text": "周二下午2点到3点，李四上英语课"
  }' | jq '.'

echo ""

# 9. 更新排课
echo "9. 更新排课..."
curl -s -X PUT "$BASE_URL/timetables/$TIMETABLE_ID/schedules/$SCHEDULE_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "studentName": "张三",
    "subject": "高等数学",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00:00",
    "endTime": "11:00:00",
    "note": "更新后的测试课程"
  }' | jq '.'

echo ""

# 10. 创建管理员用户
echo "10. 注册管理员用户..."
ADMIN_REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123",
    "email": "<EMAIL>"
  }')
echo "管理员注册响应: $ADMIN_REGISTER_RESPONSE"

echo ""

echo "=== API测试完成 ===" 
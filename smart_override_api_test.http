### 智能覆盖功能API测试

### 1. 登录获取token
POST http://localhost:8088/timetable/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 创建测试课表
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "智能覆盖测试课表",
  "description": "测试智能覆盖功能",
  "type": "WEEKLY"
}

### 3. 创建基础排课数据
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "基础数据1"
  },
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY", 
    "startTime": "14:00",
    "endTime": "15:00",
    "note": "基础数据2"
  },
  {
    "studentName": "王五",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "16:00",
    "endTime": "17:00", 
    "note": "基础数据3"
  }
]

### 4. 查看初始数据
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules
Authorization: Bearer {{token}}

### 5. 测试场景1：不同学员时间冲突（应该删除原有）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "赵六",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "测试不同学员覆盖张三"
  }
]

### 6. 强制覆盖场景1
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "赵六",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "测试不同学员覆盖张三"
  }
]

### 7. 验证场景1结果（张三应该被删除，赵六应该存在）
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules
Authorization: Bearer {{token}}

### 8. 测试场景2：同一学员重复时间（应该保留原有）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:30",
    "endTime": "15:30",
    "note": "测试同学员重复时间"
  }
]

### 9. 强制覆盖场景2
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:30",
    "endTime": "15:30",
    "note": "测试同学员重复时间"
  }
]

### 10. 验证场景2结果（李四应该有两个时间段）
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules
Authorization: Bearer {{token}}

### 11. 测试场景3：混合冲突情况
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "孙七",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "16:00",
    "endTime": "17:00",
    "note": "覆盖王五"
  },
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:45",
    "endTime": "15:45",
    "note": "李四再次重复"
  }
]

### 12. 强制覆盖场景3
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "孙七",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "16:00",
    "endTime": "17:00",
    "note": "覆盖王五"
  },
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:45",
    "endTime": "15:45",
    "note": "李四再次重复"
  }
]

### 13. 最终验证结果
GET http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules
Authorization: Bearer {{token}}

### 预期最终结果：
# 1. 赵六 周一 09:00-10:00 （覆盖了张三）
# 2. 李四 周二 14:00-15:00 （原有）
# 3. 李四 周二 14:30-15:30 （重复1）
# 4. 李四 周二 14:45-15:45 （重复2）
# 5. 孙七 周三 16:00-17:00 （覆盖了王五）

### 14. 测试日期范围课表的智能覆盖
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "日期范围智能覆盖测试",
  "description": "测试日期范围课表的智能覆盖",
  "type": "DATE_RANGE",
  "startDate": "2025-07-01",
  "endDate": "2025-07-31"
}

### 15. 为日期范围课表添加基础数据
POST http://localhost:8088/timetable/api/timetables/{{dateRangeTimetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张小啼",
    "scheduleDate": "2025-07-11",
    "startTime": "17:00",
    "endTime": "18:00",
    "note": "基础数据"
  }
]

### 16. 测试日期范围的智能覆盖
POST http://localhost:8088/timetable/api/timetables/{{dateRangeTimetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "李小明",
    "scheduleDate": "2025-07-11",
    "startTime": "17:00",
    "endTime": "18:00",
    "note": "覆盖张小啼"
  },
  {
    "studentName": "张小啼",
    "scheduleDate": "2025-07-11",
    "startTime": "17:30",
    "endTime": "18:30",
    "note": "张小啼重复时间"
  }
]

### 冲突检测功能API测试

### 1. 登录获取token
POST http://localhost:8088/timetable/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 创建测试课表
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "冲突检测测试课表",
  "description": "用于测试冲突检测功能",
  "type": "WEEKLY"
}

### 3. 创建基础排课数据（用于产生冲突）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "基础数据"
  },
  {
    "studentName": "李四",
    "dayOfWeek": "TUESDAY",
    "startTime": "14:00",
    "endTime": "15:00",
    "note": "基础数据"
  }
]

### 4. 测试冲突检测 - 学生时间冲突
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:30",
    "endTime": "10:30",
    "note": "测试学生时间冲突"
  }
]

### 5. 测试冲突检测 - 时间段占用冲突
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "王五",
    "dayOfWeek": "MONDAY",
    "startTime": "09:30",
    "endTime": "10:30",
    "note": "测试时间段冲突"
  }
]

### 6. 测试冲突检测 - 新排课之间冲突
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "赵六",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "10:00",
    "endTime": "11:00",
    "note": "新排课1"
  },
  {
    "studentName": "赵六",
    "dayOfWeek": "WEDNESDAY",
    "startTime": "10:30",
    "endTime": "11:30",
    "note": "新排课2 - 与新排课1冲突"
  }
]

### 7. 测试无冲突情况
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "孙七",
    "dayOfWeek": "FRIDAY",
    "startTime": "16:00",
    "endTime": "17:00",
    "note": "无冲突排课"
  }
]

### 8. 测试强制创建（忽略冲突）
POST http://localhost:8088/timetable/api/timetables/{{timetableId}}/schedules/batch/force
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:30",
    "endTime": "10:30",
    "note": "强制创建 - 忽略冲突"
  }
]

### 9. 测试日期范围课表冲突检测
POST http://localhost:8088/timetable/api/timetables
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "日期范围冲突测试",
  "description": "测试日期范围课表的冲突检测",
  "type": "DATE_RANGE",
  "startDate": "2025-07-01",
  "endDate": "2025-07-31"
}

### 10. 为日期范围课表添加基础数据
POST http://localhost:8088/timetable/api/timetables/{{dateRangeTimetableId}}/schedules/batch
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张小啼",
    "scheduleDate": "2025-07-11",
    "startTime": "17:00",
    "endTime": "18:00",
    "note": "基础数据"
  }
]

### 11. 测试日期范围课表冲突
POST http://localhost:8088/timetable/api/timetables/{{dateRangeTimetableId}}/schedules/batch/check-conflicts
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "studentName": "张小啼",
    "scheduleDate": "2025-07-11",
    "startTime": "17:30",
    "endTime": "18:30",
    "note": "测试日期冲突"
  }
]

### 12. 测试时间平铺后的冲突检测
POST http://localhost:8088/timetable/api/timetables/{{dateRangeTimetableId}}/schedules/format
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "text": "7.11-7.13，17-19，张小啼",
  "type": "DATE_RANGE"
}

### 预期结果说明
# 测试4: 应该检测到学生张三在周一9:30-10:30与现有9:00-10:00课程冲突
# 测试5: 应该检测到王五与张三在周一9:30-10:30时间段冲突
# 测试6: 应该检测到赵六的两个新排课之间存在时间冲突
# 测试7: 应该返回无冲突
# 测试8: 应该成功创建，忽略冲突
# 测试11: 应该检测到张小啼在7月11日17:30-18:30与现有17:00-18:00课程冲突
# 测试12: 解析后应该检测到多个时间段的冲突

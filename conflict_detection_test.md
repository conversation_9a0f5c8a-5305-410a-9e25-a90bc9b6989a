# 冲突检测功能测试文档

## 功能概述
当用户录入课表信息后，系统会自动检测时间冲突，并提供解决方案。

## 冲突类型
1. **学生时间冲突**：同一学生在同一时间段有多个课程
2. **时间段占用冲突**：不同学生在同一时间段产生冲突
3. **新排课冲突**：本次录入的多个排课之间存在冲突

## 实现架构

### 后端实现
1. **ConflictInfo.java** - 冲突信息DTO
2. **ConflictCheckResult.java** - 冲突检测结果DTO
3. **ScheduleService.checkScheduleConflicts()** - 冲突检测核心逻辑
4. **ScheduleController** - 新增API端点：
   - `POST /batch/check-conflicts` - 检查冲突
   - `POST /batch/force` - 强制创建（忽略冲突）

### 前端实现
1. **timetable.jsx** - 新增API调用方法
2. **ConfirmSchedulePage.jsx** - 冲突检测UI和逻辑

## 用户流程
1. 用户输入文本解析排课信息
2. 在确认页面点击"确认"
3. 系统自动检查冲突
4. 如有冲突：
   - 显示冲突详情模态框
   - 用户选择"取消"或"强制覆盖"
5. 如无冲突：
   - 直接创建排课

## 测试用例

### 测试用例1：学生时间冲突
**前置条件：**
- 已有排课：张三，周一 9:00-10:00

**测试输入：**
```
张三，周一，9:30-10:30
```

**预期结果：**
- 检测到学生时间冲突
- 显示冲突信息："学生 张三 在 MONDAY 09:30-10:30 已有课程安排"

### 测试用例2：时间段占用冲突
**前置条件：**
- 已有排课：张三，周一 9:00-10:00

**测试输入：**
```
李四，周一，9:30-10:30
```

**预期结果：**
- 检测到时间段冲突
- 显示冲突信息："时间段 MONDAY 09:30-10:30 已被学生 张三 占用，新学生 李四 产生冲突"

### 测试用例3：新排课冲突
**测试输入：**
```
张三，周一，9:00-10:00
张三，周一，9:30-10:30
```

**预期结果：**
- 检测到新排课冲突
- 显示冲突信息："学生 张三 在 MONDAY 09:00-10:00 有重复的课程安排"

### 测试用例4：时间平铺 + 冲突检测
**测试输入：**
```
7.11-7.13，5-7，张小啼
```

**预期结果：**
- 解析为6个排课（3天 × 2小时）
- 检查每个1小时时间段的冲突
- 如有冲突，显示具体的冲突时间段

## API测试

### 1. 检查冲突API
```http
POST /timetables/{id}/schedules/batch/check-conflicts
Content-Type: application/json

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY",
    "startTime": "09:00",
    "endTime": "10:00"
  }
]
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "hasConflicts": true,
    "totalConflicts": 1,
    "summary": "发现 1 个冲突： 学生时间冲突 1 个",
    "conflicts": [
      {
        "newScheduleIndex": 0,
        "conflictType": "STUDENT_TIME_CONFLICT",
        "conflictDescription": "学生 张三 在 MONDAY 09:00-10:00 已有课程安排",
        "existingSchedule": {
          "id": 1,
          "studentName": "张三",
          "dayOfWeek": "MONDAY",
          "startTime": "09:00",
          "endTime": "10:00"
        }
      }
    ]
  }
}
```

### 2. 强制创建API
```http
POST /timetables/{id}/schedules/batch/force
Content-Type: application/json

[
  {
    "studentName": "张三",
    "dayOfWeek": "MONDAY", 
    "startTime": "09:00",
    "endTime": "10:00",
    "note": "通过文本识别创建（强制覆盖）"
  }
]
```

## 验证步骤
1. 启动后端服务
2. 启动前端服务
3. 创建测试课表
4. 添加一些基础排课数据
5. 使用文本解析功能输入冲突的排课
6. 验证冲突检测和处理流程

## 预期改进
1. 支持部分冲突的选择性处理
2. 冲突解决建议（如时间调整建议）
3. 批量冲突处理优化
4. 更详细的冲突分析报告

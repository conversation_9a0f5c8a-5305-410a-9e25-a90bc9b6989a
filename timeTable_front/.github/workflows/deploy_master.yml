name: deploy_master_front

on:
  push:
    branches:
      - master

jobs:
  master-front-deploy:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ secrets.NODE_VERSION }}

      - name: Install Dependencies
        run: npm install

      - name: Build
        run: npm run build
      
      - name: clean Server Old Files
        uses: fifsky/ssh-action@master
        with:
          command: rm -rf ${{ secrets.TARGET_PATH }}/*
          host: ${{ secrets.HW_TEST_SERVER }}
          user: ${{ secrets.HW_SERVER_USERNAME }}
          pass: ${{ secrets.HW_SERVER_PWD }}
          args: "-tt"

      - name: Scp dist To HW Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HW_TEST_SERVER }}
          username: ${{ secrets.HW_SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: ${{ github.workspace }}/dist/*
          target: ${{ secrets.TARGET_PATH }}
          strip_components: ${{ secrets.STRIP_COMPONENTS }}
/**
 * 音频处理工具函数
 * 提供音频格式转换、重采样和压缩功能
 */

/**
 * 将任意格式的音频Blob转换为标准的16kHz WAV格式
 * @param {Blob} audioBlob - 输入的音频Blob
 * @returns {Promise<Blob>} - 转换后的16kHz WAV格式Blob
 */
export const convertToWav = async (audioBlob) => {
  const TARGET_SAMPLE_RATE = 16000;
  const fileReader = new FileReader();

  return new Promise((resolve, reject) => {
    fileReader.onerror = () => {
      fileReader.abort();
      reject(new DOMException("Problem parsing input file."));
    };

    fileReader.onload = async (e) => {
      try {
        const arrayBuffer = e.target.result;
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        // 解码原始音频数据
        const decodedBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // 如果采样率已经是16kHz，则直接编码
        if (decodedBuffer.sampleRate === TARGET_SAMPLE_RATE) {
          console.log("音频采样率已是16kHz，直接进行WAV编码。");
          const wavBuffer = audioBufferToWav(decodedBuffer);
          resolve(new Blob([wavBuffer], { type: 'audio/wav' }));
          return;
        }

        // 如果采样率不匹配，则进行重采样
        console.log(`需要重采样: 从 ${decodedBuffer.sampleRate}Hz 到 ${TARGET_SAMPLE_RATE}Hz`);
        const offlineContext = new OfflineAudioContext(
          decodedBuffer.numberOfChannels,
          (decodedBuffer.duration * TARGET_SAMPLE_RATE),
          TARGET_SAMPLE_RATE
        );
        
        const bufferSource = offlineContext.createBufferSource();
        bufferSource.buffer = decodedBuffer;
        bufferSource.connect(offlineContext.destination);
        bufferSource.start();

        const resampledBuffer = await offlineContext.startRendering();
        
        // 将重采样后的buffer编码为WAV
        const wavBuffer = audioBufferToWav(resampledBuffer);
        resolve(new Blob([wavBuffer], { type: 'audio/wav' }));

      } catch (error) {
        console.error('音频转换或重采样失败:', error);
        reject(error);
      }
    };

    fileReader.readAsArrayBuffer(audioBlob);
  });
};

/**
 * 将AudioBuffer编码为WAV格式的ArrayBuffer (核心函数)
 * @param {AudioBuffer} buffer - Web Audio API的音频缓冲区
 * @returns {ArrayBuffer} - WAV格式的二进制数据
 */
function audioBufferToWav(buffer) {
  const numOfChan = buffer.numberOfChannels;
  const length = buffer.length * numOfChan * 2 + 44;
  const arrayBuffer = new ArrayBuffer(length);
  const view = new DataView(arrayBuffer);
  const channels = [];
  let i = 0;
  let sample = 0;
  let offset = 0;

  // 写入WAV文件头
  setString(view, 0, 'RIFF');
  view.setUint32(4, length - 8, true);
  setString(view, 8, 'WAVE');
  setString(view, 12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, numOfChan, true);
  view.setUint32(24, buffer.sampleRate, true);
  view.setUint32(28, buffer.sampleRate * 2 * numOfChan, true);
  view.setUint16(32, numOfChan * 2, true);
  view.setUint16(34, 16, true);
  setString(view, 36, 'data');
  view.setUint32(40, length - 44, true);

  // 写入PCM数据
  for (i = 0; i < buffer.numberOfChannels; i++) {
    channels.push(buffer.getChannelData(i));
  }

  offset = 44;
  for (i = 0; i < buffer.length; i++) {
    for (let j = 0; j < numOfChan; j++) {
      sample = Math.max(-1, Math.min(1, channels[j][i]));
      sample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset, sample, true);
      offset += 2;
    }
  }
  return arrayBuffer;
}

function setString(view, offset, str) {
  for (let i = 0; i < str.length; i++) {
    view.setUint8(offset + i, str.charCodeAt(i));
  }
}

/**
 * 检查浏览器支持的音频格式
 * @returns {string} - 支持的最佳音频格式
 */
export const getSupportedAudioMimeType = () => {
  const supportedTypes = [
    'audio/mpeg', // 优先尝试MP3
    'audio/webm;codecs=opus',
    'audio/mp4',
    'audio/ogg;codecs=opus',
    'audio/wav',
    ''
  ];
  return supportedTypes.find(type => MediaRecorder.isTypeSupported(type)) || '';
};

/**
 * 获取优化的音频录制参数
 * @returns {object} - 音频录制配置
 */
export const getOptimalAudioConstraints = () => ({
  audio: {
    sampleRate: { ideal: 16000 }, // 尝试请求16kHz
    channelCount: 1,
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
  },
});

/**
 * 压缩音频文件大小（通过降低采样率和重采样）
 * @param {Blob} audioBlob - 输入的音频Blob
 * @param {number} targetSizeKB - 目标文件大小（KB）
 * @returns {Promise<Blob>} - 压缩后的音频Blob
 */
export const compressAudio = async (audioBlob, targetSizeKB = 1536) => {
  if (audioBlob.size <= targetSizeKB * 1024) {
    return audioBlob;
  }
  console.log(`音频文件过大 (${(audioBlob.size / 1024).toFixed(2)} KB)，尝试压缩到 ${targetSizeKB} KB 以下`);
  
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const arrayBuffer = await audioBlob.arrayBuffer();
    const originalBuffer = await audioContext.decodeAudioData(arrayBuffer);

    const duration = originalBuffer.duration;
    const targetSizeBytes = targetSizeKB * 1024;
    
    // 目标采样率 = (目标大小 - wav头大小) / (时长 * 声道数 * 2字节)
    const targetSampleRate = (targetSizeBytes - 44) / (duration * originalBuffer.numberOfChannels * 2);
    
    // 不能低于8kHz，否则语音质量太差
    const finalSampleRate = Math.max(8000, Math.min(originalBuffer.sampleRate, targetSampleRate));

    if (finalSampleRate >= originalBuffer.sampleRate) {
        console.warn("无法进一步压缩，返回原始文件。");
        return audioBlob;
    }

    console.log(`压缩采样率: 从 ${originalBuffer.sampleRate}Hz 降至 ${Math.round(finalSampleRate)}Hz`);

    const offlineContext = new OfflineAudioContext(
      originalBuffer.numberOfChannels,
      duration * finalSampleRate,
      finalSampleRate
    );

    const bufferSource = offlineContext.createBufferSource();
    bufferSource.buffer = originalBuffer;
    bufferSource.connect(offlineContext.destination);
    bufferSource.start(0);

    const compressedBuffer = await offlineContext.startRendering();
    const wavBuffer = audioBufferToWav(compressedBuffer);
    
    const compressedBlob = new Blob([wavBuffer], { type: 'audio/wav' });
    console.log(`压缩后文件大小: ${(compressedBlob.size / 1024).toFixed(2)} KB`);
    
    return compressedBlob;
  } catch (error) {
    console.error('音频压缩失败:', error);
    return audioBlob; // 压缩失败时返回原文件
  }
};

/**
 * 创建直接录制WAV格式的MediaRecorder
 * @param {MediaStream} stream - 媒体流
 * @param {function} onDataAvailable - 数据可用时的回调
 * @returns {MediaRecorder} - 配置好的MediaRecorder实例
 */
export const createWavRecorder = (stream, onDataAvailable) => {
  // 尝试直接使用WAV格式
  let mimeType = 'audio/wav';
  
  // 如果浏览器不支持WAV，则使用支持的格式后续转换
  if (!MediaRecorder.isTypeSupported(mimeType)) {
    mimeType = getSupportedAudioMimeType();
    console.log('浏览器不支持直接录制WAV，使用格式:', mimeType);
  } else {
    console.log('使用WAV格式直接录制');
  }
  
  const options = {
    mimeType: mimeType,
    audioBitsPerSecond: 16000 // 设置较低的比特率以减小文件大小
  };
  
  const mediaRecorder = new MediaRecorder(stream, options);
  
  mediaRecorder.ondataavailable = (event) => {
    if (event.data.size > 0) {
      onDataAvailable(event.data);
    }
  };
  
  return mediaRecorder;
}; 
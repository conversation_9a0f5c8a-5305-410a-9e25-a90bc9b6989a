/* Dashboard.css */

.timetable-list .ant-list-item {
  transition: background-color 0.3s, box-shadow 0.3s;
  background: #fff;
  padding: 0;
  border-radius: 8px;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
}

.timetable-list .ant-list-item:hover {
  background-color: #f9f9f9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: transparent;
}

.timetable-list .ant-list-item .ant-list-item-meta {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 24px 12px 24px;
  box-sizing: border-box;
}

.timetable-list .ant-list-item .ant-list-item-action {
  padding: 10px 24px !important;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 24px;
}

.timetable-item-meta .ant-list-item-meta-content {
  display: flex;
  flex-direction: column;
}

.timetable-item-meta .ant-list-item-meta-title {
  margin-bottom: 8px !important;
}

/* 响应式布局 for Mobile */
@media (max-width: 768px) {
  .page-container {
    padding: 1rem;
  }

  .timetable-list .ant-list-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }

  .timetable-list .ant-list-item .ant-list-item-meta {
    width: 100%;
    margin-bottom: 0;
    padding: 10px 1rem 12px 1rem;
  }
  
  .timetable-list .ant-list-item .ant-list-item-meta-avatar {
     margin-right: 0;
     margin-bottom: 16px;
  }

  .timetable-list .ant-list-item .ant-list-item-action {
    margin-left: 0;
    padding: 10px 1rem !important;
    border-top: 1px solid #f0f0f0;
  }
} 
.confirm-schedule-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 1rem;
}

p {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2rem;
}

.schedule-table th,
.schedule-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.schedule-table th {
  background-color: #f2f2f2;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.schedule-table input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

.schedule-table input[type="text"]:focus {
  border-color: #007bff;
  outline: none;
}

.action-cell {
  text-align: center;
}

.delete-btn-small {
  font-size: 16px;
  padding: 4px;
  line-height: 1;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.confirm-btn {
  background-color: #28a745;
  color: white;
}

.confirm-btn:hover {
  background-color: #218838;
}

.cancel-btn {
  background-color: #dc3545;
  color: white;
}

.cancel-btn:hover {
  background-color: #c82333;
} 
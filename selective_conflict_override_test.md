# 选择性冲突覆盖功能测试

## 功能说明

新增了选择性冲突覆盖功能，用户可以：
1. **查看所有冲突**：显示完整的冲突列表
2. **选择性覆盖**：通过复选框选择需要覆盖的冲突
3. **批量操作**：支持全选/取消全选
4. **智能提示**：按钮显示选中数量，禁用状态管理

## 界面设计

### 冲突列表界面
```
发现排课冲突

✅ 已成功创建 2 个排课

冲突详情：                    ☑️ 全选

☑️ [时间冲突] 时间段 周一 09:00-10:00 已被学生 张三 占用，新学生 王五 产生冲突
   现有课程：张三 - 09:00-10:00 (周一)

☐ [学生冲突] 学生 李四 在 周二 14:00-15:00 已有课程安排  
   现有课程：李四 - 14:00-15:00 (周二)

☑️ [时间冲突] 时间段 周三 16:00-17:00 已被学生 赵六 占用，新学生 孙七 产生冲突
   现有课程：赵六 - 16:00-17:00 (周三)

ℹ️ 请勾选需要强制覆盖的冲突项，然后点击"强制覆盖选中项"。无冲突的排课已成功创建，强制覆盖规则：...

[取消]  [强制覆盖选中项 (2)]
```

### 按钮状态
- **未选中任何冲突**：按钮禁用，显示"强制覆盖选中项 (0)"
- **选中部分冲突**：按钮可用，显示"强制覆盖选中项 (2)"
- **全选冲突**：按钮可用，显示"强制覆盖选中项 (3)"

## 测试场景

### 场景1：多冲突选择性覆盖
**前置条件**：
- 已有：张三，周一 9:00-10:00
- 已有：李四，周二 14:00-15:00  
- 已有：赵六，周三 16:00-17:00

**输入**：
```
王五，周一，9:00-10:00    # 与张三冲突
李四，周二，14:30-15:30   # 与李四自己冲突
孙七，周三，16:00-17:00   # 与赵六冲突
陈八，周四，18:00-19:00   # 无冲突
```

**预期结果**：
1. **立即创建**：陈八的排课 ✓
2. **显示冲突**：3个冲突项，默认全选
3. **用户操作**：取消选择李四的冲突，只选择王五和孙七
4. **强制覆盖**：只处理王五和孙七的冲突
5. **最终结果**：
   - 陈八：周四 18:00-19:00 ✓
   - 王五：周一 9:00-10:00 ✓ (覆盖张三)
   - 孙七：周三 16:00-17:00 ✓ (覆盖赵六)
   - 李四冲突：未处理，保持原状

### 场景2：全选/取消全选
**操作流程**：
1. 显示3个冲突，默认全选
2. 点击"全选"取消 → 所有冲突取消选择
3. 按钮变为禁用状态："强制覆盖选中项 (0)"
4. 手动选择2个冲突
5. 按钮变为可用："强制覆盖选中项 (2)"
6. 点击"全选"勾选 → 所有冲突被选中
7. 按钮显示："强制覆盖选中项 (3)"

### 场景3：部分选择的提示信息
**选择2个冲突，跳过1个冲突**：

**预期提示**：
```
排课已强制创建成功！共创建 4 个排课，跳过 1 个未选中的冲突
```

## 技术实现

### 前端状态管理
```javascript
const [selectedConflicts, setSelectedConflicts] = useState(new Set());

// 初始化：默认全选
setSelectedConflicts(new Set(conflictResult.conflicts.map((_, index) => index)));

// 单个选择
const handleConflictSelection = (conflictIndex, checked) => {
  const newSelected = new Set(selectedConflicts);
  if (checked) {
    newSelected.add(conflictIndex);
  } else {
    newSelected.delete(conflictIndex);
  }
  setSelectedConflicts(newSelected);
};

// 全选/取消全选
const handleSelectAllConflicts = (checked) => {
  if (checked) {
    setSelectedConflicts(new Set(conflicts.conflicts.map((_, index) => index)));
  } else {
    setSelectedConflicts(new Set());
  }
};
```

### 强制覆盖逻辑
```javascript
// 只处理选中的冲突
const selectedConflictRequests = conflicts.conflicts
  .filter((_, index) => selectedConflicts.has(index))
  .map(conflict => {
    const request = conflict.newScheduleRequest || conflict.newSchedule;
    return {
      studentName: request.studentName,
      dayOfWeek: request.dayOfWeek,
      scheduleDate: request.scheduleDate,
      startTime: request.startTime,
      endTime: request.endTime,
      note: '通过文本识别创建（智能覆盖）',
    };
  });
```

### 界面组件
```jsx
// 全选复选框
<Checkbox
  checked={selectedConflicts.size === conflicts.conflicts.length}
  indeterminate={selectedConflicts.size > 0 && selectedConflicts.size < conflicts.conflicts.length}
  onChange={(e) => handleSelectAllConflicts(e.target.checked)}
>
  全选
</Checkbox>

// 单个冲突复选框
<Checkbox
  checked={selectedConflicts.has(index)}
  onChange={(e) => handleConflictSelection(index, e.target.checked)}
/>

// 强制覆盖按钮
<Button 
  type="primary" 
  danger 
  onClick={handleForceCreate} 
  loading={loading}
  disabled={selectedConflicts.size === 0}
>
  强制覆盖选中项 ({selectedConflicts.size})
</Button>
```

## 用户体验优化

### 1. 默认行为
- 显示冲突时默认全选，保持向后兼容
- 用户可以根据需要取消选择

### 2. 视觉反馈
- 复选框状态清晰显示选择情况
- 全选复选框支持半选状态
- 按钮显示选中数量

### 3. 操作提示
- 未选择时按钮禁用并提示
- 操作完成后显示详细的结果统计
- 区分已创建、新覆盖、跳过的数量

### 4. 错误处理
- 未选择任何冲突时显示警告
- 操作失败时保持选择状态

## 验证清单

- [ ] 冲突列表显示复选框
- [ ] 默认全选所有冲突
- [ ] 单个冲突可以选择/取消
- [ ] 全选复选框工作正常
- [ ] 半选状态显示正确
- [ ] 按钮状态根据选择动态变化
- [ ] 未选择时按钮禁用
- [ ] 只覆盖选中的冲突
- [ ] 跳过未选中的冲突
- [ ] 结果提示信息准确
- [ ] 操作完成后正确跳转

## API影响

后端API无需修改，前端只是过滤要发送的冲突请求：
- 原来：发送所有冲突的排课请求
- 现在：只发送选中冲突的排课请求

这样既保持了API的稳定性，又提供了更灵活的用户体验。

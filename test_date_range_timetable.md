# 日期范围课表修复测试

## 问题描述
日期范围课表无法正确显示内容，但周固定课表可以正常显示。

## 修复内容

### 1. 前端修复 (ViewTimetable.jsx)
- 配置dayjs以周一为一周的开始
- 修复周计算逻辑，与后端保持一致
- 优化数据获取，按周获取数据而不是客户端过滤
- 修复总周数计算

### 2. 关键修改点

#### dayjs配置
```javascript
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';

dayjs.extend(weekday);
dayjs.extend(localeData);

// 设置周一为一周的开始
dayjs.locale({
  ...dayjs.Ls.en,
  weekStart: 1
});
```

#### 周计算逻辑
```javascript
// 前端 - 与后端保持一致
const anchorMonday = startDate.startOf('week');
const weekStart = anchorMonday.add(currentWeek - 1, 'week');
```

```java
// 后端 - 原有逻辑
LocalDate anchorMonday = timetableStartDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
LocalDate weekStartDate = anchorMonday.plusWeeks(week - 1);
```

#### 数据获取优化
```javascript
// 对于日期范围课表，按周获取数据
const week = timetable && !timetable.isWeekly ? currentWeek : null;
const response = await getTimetableSchedules(timetableId, week);
```

## 测试步骤

### 1. 创建日期范围课表
- 起始日期：2025-07-01 (周二)
- 结束日期：2025-07-07 (周一)
- 预期：第1周从2025-06-30(周一)开始，到2025-07-06(周日)结束

### 2. 添加测试数据
```json
[
  {
    "studentName": "张三",
    "scheduleDate": "2025-07-01",
    "startTime": "09:00",
    "endTime": "10:00"
  },
  {
    "studentName": "李四", 
    "scheduleDate": "2025-07-02",
    "startTime": "10:00",
    "endTime": "11:00"
  },
  {
    "studentName": "王五",
    "scheduleDate": "2025-07-03", 
    "startTime": "11:00",
    "endTime": "12:00"
  }
]
```

### 3. 验证结果
- 第1周应该显示所有3个学生的课程
- 日期显示应该正确：07-01, 07-02, 07-03
- 学生姓名应该正确渲染在对应的时间格子中

## 预期修复效果
1. 日期范围课表能够正确显示学生姓名
2. 周数计算正确，与后端逻辑一致
3. 日期显示正确，周一为每周第一天
4. 分页功能正常工作
